"use client"

import { useState } from "react"
import { Search, Plus, Eye, User, Calendar, MapPin, Tag, Edit, Trash2 } from "lucide-react"
import { Navigation } from "@/components/navigation"

// Mock user data
const mockUser = {
  name: "<PERSON>",
  email: "<EMAIL>",
  role: "Student"
}

// Mock user's posts
const mockUserPosts = [
  {
    id: 1,
    type: "lost",
    title: "iPhone 14 Pro",
    description: "Black iPhone 14 Pro with a clear case. Lost near the library.",
    category: "Electronics",
    date: "2024-01-15",
    location: "Main Library",
    status: "active",
    views: 23,
    responses: 2
  },
  {
    id: 2,
    type: "found",
    title: "Red Backpack",
    description: "Found a red Jansport backpack near the parking lot.",
    category: "Bags",
    date: "2024-01-16",
    location: "Main Parking Lot",
    status: "available",
    views: 15,
    responses: 1
  }
]

// Mock claim requests
const mockClaimRequests = [
  {
    id: 1,
    itemTitle: "Red Backpack",
    claimerName: "<PERSON>",
    claimerEmail: "<EMAIL>",
    message: "I think this is my backpack. I lost it yesterday near the parking area.",
    date: "2024-01-17",
    status: "pending"
  }
]

export default function DashboardPage() {
  const [activeTab, setActiveTab] = useState("posts")

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">Dashboard</h1>
          <p className="text-muted-foreground">
            Manage your lost and found items
          </p>
        </div>

        {/* User Info */}
        <div className="bg-card border border-border rounded-lg p-6 mb-8">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-primary/10 text-primary rounded-full">
              <User className="h-8 w-8" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-card-foreground">{mockUser.name}</h2>
              <p className="text-muted-foreground">{mockUser.email}</p>
              <p className="text-sm text-muted-foreground">{mockUser.role}</p>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid md:grid-cols-4 gap-4 mb-8">
          <div className="bg-card border border-border rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Posts</p>
                <p className="text-2xl font-bold text-card-foreground">{mockUserPosts.length}</p>
              </div>
              <Search className="h-8 w-8 text-primary" />
            </div>
          </div>
          
          <div className="bg-card border border-border rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Lost Items</p>
                <p className="text-2xl font-bold text-card-foreground">
                  {mockUserPosts.filter(p => p.type === "lost").length}
                </p>
              </div>
              <Search className="h-8 w-8 text-destructive" />
            </div>
          </div>
          
          <div className="bg-card border border-border rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Found Items</p>
                <p className="text-2xl font-bold text-card-foreground">
                  {mockUserPosts.filter(p => p.type === "found").length}
                </p>
              </div>
              <Eye className="h-8 w-8 text-primary" />
            </div>
          </div>
          
          <div className="bg-card border border-border rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Pending Claims</p>
                <p className="text-2xl font-bold text-card-foreground">{mockClaimRequests.length}</p>
              </div>
              <User className="h-8 w-8 text-secondary" />
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="mb-6">
          <div className="border-b border-border">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab("posts")}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === "posts"
                    ? "border-primary text-primary"
                    : "border-transparent text-muted-foreground hover:text-foreground hover:border-border"
                }`}
              >
                My Posts
              </button>
              <button
                onClick={() => setActiveTab("claims")}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === "claims"
                    ? "border-primary text-primary"
                    : "border-transparent text-muted-foreground hover:text-foreground hover:border-border"
                }`}
              >
                Claim Requests
              </button>
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === "posts" && (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold text-foreground">Your Posts</h3>
              <div className="space-x-2">
                <button className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors">
                  <Plus className="h-4 w-4 inline mr-2" />
                  Report Lost
                </button>
                <button className="bg-secondary text-secondary-foreground px-4 py-2 rounded-md hover:bg-secondary/80 transition-colors">
                  <Plus className="h-4 w-4 inline mr-2" />
                  Report Found
                </button>
              </div>
            </div>
            
            <div className="space-y-4">
              {mockUserPosts.map((post) => (
                <div key={post.id} className="bg-card border border-border rounded-lg p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          post.type === "lost" 
                            ? "bg-destructive/10 text-destructive" 
                            : "bg-primary/10 text-primary"
                        }`}>
                          {post.type.toUpperCase()}
                        </span>
                        <span className="px-2 py-1 rounded text-xs font-medium bg-secondary/10 text-secondary">
                          {post.status.toUpperCase()}
                        </span>
                      </div>
                      <h4 className="text-lg font-semibold text-card-foreground mb-2">{post.title}</h4>
                      <p className="text-muted-foreground mb-3">{post.description}</p>
                      
                      <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center">
                          <Tag className="h-4 w-4 mr-1" />
                          {post.category}
                        </div>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          {new Date(post.date).toLocaleDateString()}
                        </div>
                        <div className="flex items-center">
                          <MapPin className="h-4 w-4 mr-1" />
                          {post.location}
                        </div>
                        <div className="flex items-center">
                          <Eye className="h-4 w-4 mr-1" />
                          {post.views} views
                        </div>
                        <div className="flex items-center">
                          <User className="h-4 w-4 mr-1" />
                          {post.responses} responses
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex space-x-2 ml-4">
                      <button className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded">
                        <Edit className="h-4 w-4" />
                      </button>
                      <button className="p-2 text-muted-foreground hover:text-destructive hover:bg-accent rounded">
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === "claims" && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-foreground">Claim Requests</h3>
            
            <div className="space-y-4">
              {mockClaimRequests.map((claim) => (
                <div key={claim.id} className="bg-card border border-border rounded-lg p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex-1">
                      <h4 className="text-lg font-semibold text-card-foreground mb-2">
                        Claim for: {claim.itemTitle}
                      </h4>
                      <div className="space-y-2 text-sm">
                        <p><span className="font-medium">From:</span> {claim.claimerName} ({claim.claimerEmail})</p>
                        <p><span className="font-medium">Date:</span> {new Date(claim.date).toLocaleDateString()}</p>
                        <p><span className="font-medium">Message:</span> {claim.message}</p>
                      </div>
                    </div>
                    
                    <div className="flex space-x-2 ml-4">
                      <button className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors">
                        Approve
                      </button>
                      <button className="bg-destructive text-destructive-foreground px-4 py-2 rounded-md hover:bg-destructive/90 transition-colors">
                        Reject
                      </button>
                    </div>
                  </div>
                </div>
              ))}
              
              {mockClaimRequests.length === 0 && (
                <div className="text-center py-8">
                  <User className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h4 className="text-lg font-semibold text-foreground mb-2">No claim requests</h4>
                  <p className="text-muted-foreground">You don't have any pending claim requests.</p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
