export type Options = [
    {
        ignoreStringArrays?: boolean;
    }
];
export type MessageIds = 'requireCompare';
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<"requireCompare", Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=require-array-sort-compare.d.ts.map