"use client"

import { useState } from "react"
import { Search } from "lucide-react"
import { Navigation } from "@/components/navigation"
import { SearchFilters } from "@/components/search-filters"
import { ItemCard } from "@/components/item-card"
import type { LostItem } from "@/lib/utils"

// Mock data for demonstration
const mockLostItems: LostItem[] = [
  {
    id: "1",
    title: "iPhone 14 Pro",
    description: "Black iPhone 14 Pro with a clear case. Lost near the library.",
    category: "Electronics",
    dateLost: "2024-01-15",
    location: "Main Library",
    contactInfo: "<EMAIL>",
    status: "active",
    userId: "user1",
    createdAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-01-15T10:00:00Z"
  },
  {
    id: "2",
    title: "Blue Water Bottle",
    description: "Stainless steel water bottle with UMT sticker.",
    category: "Personal Items",
    dateLost: "2024-01-14",
    location: "Cafeteria",
    contactInfo: "<EMAIL>",
    status: "active",
    userId: "user2",
    createdAt: "2024-01-14T10:00:00Z",
    updatedAt: "2024-01-14T10:00:00Z"
  },
  {
    id: "3",
    title: "Calculus Textbook",
    description: "Advanced Calculus by <PERSON>, 8th edition.",
    category: "Books",
    dateLost: "2024-01-13",
    location: "Engineering Building",
    contactInfo: "<EMAIL>",
    status: "active",
    userId: "user3",
    createdAt: "2024-01-13T10:00:00Z",
    updatedAt: "2024-01-13T10:00:00Z"
  }
]

export default function LostItemsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [selectedLocation, setSelectedLocation] = useState("")
  const [filteredItems, setFilteredItems] = useState(mockLostItems)

  const handleSearch = (term: string) => {
    setSearchTerm(term)
    filterItems(term, selectedCategory, selectedLocation)
  }

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category)
    filterItems(searchTerm, category, selectedLocation)
  }

  const handleLocationChange = (location: string) => {
    setSelectedLocation(location)
    filterItems(searchTerm, selectedCategory, location)
  }

  const filterItems = (term: string, category: string, location: string) => {
    let filtered = mockLostItems

    if (term) {
      filtered = filtered.filter(item =>
        item.title.toLowerCase().includes(term.toLowerCase()) ||
        item.description.toLowerCase().includes(term.toLowerCase()) ||
        item.location.toLowerCase().includes(term.toLowerCase())
      )
    }

    if (category !== "All") {
      filtered = filtered.filter(item => item.category === category)
    }

    if (location) {
      filtered = filtered.filter(item => item.location === location)
    }

    setFilteredItems(filtered)
  }

  const handleContactOwner = (item: LostItem) => {
    // In a real app, this would open a contact modal or send a message
    alert(`Contact ${item.contactInfo} about "${item.title}"`)
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">Lost Items</h1>
          <p className="text-muted-foreground">
            Browse items that have been reported as lost on campus
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8">
          <SearchFilters
            searchTerm={searchTerm}
            onSearchChange={handleSearch}
            selectedCategory={selectedCategory}
            onCategoryChange={handleCategoryChange}
            selectedLocation={selectedLocation}
            onLocationChange={handleLocationChange}
            showAdvancedFilters={true}
          />
        </div>

        {/* Results Count */}
        <div className="mb-6">
          <p className="text-sm text-muted-foreground">
            Showing {filteredItems.length} of {mockLostItems.length} lost items
          </p>
        </div>

        {/* Items Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredItems.map((item) => (
            <ItemCard
              key={item.id}
              item={item}
              type="lost"
              onContact={() => handleContactOwner(item)}
            />
          ))}
        </div>

        {/* Empty State */}
        {filteredItems.length === 0 && (
          <div className="text-center py-12">
            <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-foreground mb-2">
              No items found
            </h3>
            <p className="text-muted-foreground">
              Try adjusting your search terms or filters
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
