import type { TSESLint } from '@typescript-eslint/utils';
export type Options = [
    {
        fixToUnknown?: boolean;
        ignoreRestArgs?: boolean;
    }
];
export type MessageIds = 'suggestNever' | 'suggestPropertyKey' | 'suggestUnknown' | 'unexpectedAny';
declare const _default: TSESLint.RuleModule<MessageIds, Options, import("../../rules").ESLintPluginDocs, TSESLint.RuleListener>;
export default _default;
//# sourceMappingURL=no-explicit-any.d.ts.map