"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.scripthost = void 0;
const base_config_1 = require("./base-config");
exports.scripthost = {
    libs: [],
    variables: [
        ['ActiveXObject', base_config_1.TYPE_VALUE],
        ['ITextWriter', base_config_1.TYPE],
        ['TextStreamBase', base_config_1.TYPE],
        ['TextStreamWriter', base_config_1.TYPE],
        ['TextStreamReader', base_config_1.TYPE],
        ['SafeArray', base_config_1.TYPE_VALUE],
        ['Enumerator', base_config_1.TYPE_VALUE],
        ['EnumeratorConstructor', base_config_1.TYPE],
        ['VBArray', base_config_1.TYPE_VALUE],
        ['VBArrayConstructor', base_config_1.TYPE],
        ['VarDate', base_config_1.TYPE_VALUE],
        ['DateConstructor', base_config_1.TYPE],
        ['Date', base_config_1.TYPE],
    ],
};
