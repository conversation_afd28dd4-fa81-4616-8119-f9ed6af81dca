{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/lostFound/lost-found-portal/src/components/theme-toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Moon, Sun } from \"lucide-react\"\nimport { useTheme } from \"next-themes\"\n\nexport function ThemeToggle() {\n  const { theme, setTheme } = useTheme()\n  const [mounted, setMounted] = React.useState(false)\n\n  React.useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  if (!mounted) {\n    return null\n  }\n\n  return (\n    <button\n      onClick={() => setTheme(theme === \"light\" ? \"dark\" : \"light\")}\n      className=\"relative inline-flex h-10 w-10 items-center justify-center rounded-md border border-gray-200 bg-white text-gray-900 transition-colors hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100 dark:hover:bg-gray-700\"\n      aria-label=\"Toggle theme\"\n    >\n      <Sun className=\"h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n      <Moon className=\"absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAMO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,SAAS,IAAM,SAAS,UAAU,UAAU,SAAS;QACrD,WAAU;QACV,cAAW;;0BAEX,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;0BACf,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;;;;;;;AAGtB", "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/lostFound/lost-found-portal/src/components/navigation.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from \"next/link\"\nimport { usePathname } from \"next/navigation\"\nimport { Search, Plus, User, Menu, X } from \"lucide-react\"\nimport { ThemeToggle } from \"./theme-toggle\"\nimport { useState } from \"react\"\n\nexport function Navigation() {\n  const pathname = usePathname()\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n\n  const navItems = [\n    { href: \"/\", label: \"Home\" },\n    { href: \"/lost-items\", label: \"Lost Items\" },\n    { href: \"/found-items\", label: \"Found Items\" },\n    { href: \"/post-lost\", label: \"Report Lost\" },\n    { href: \"/post-found\", label: \"Report Found\" },\n  ]\n\n  return (\n    <nav className=\"sticky top-0 z-50 w-full border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <Search className=\"h-8 w-8 text-primary\" />\n            <span className=\"text-xl font-bold text-foreground\">\n              Lost & Found Portal\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-6\">\n            {navItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className={`text-sm font-medium transition-colors hover:text-primary ${\n                  pathname === item.href\n                    ? \"text-primary\"\n                    : \"text-muted-foreground\"\n                }`}\n              >\n                {item.label}\n              </Link>\n            ))}\n          </div>\n\n          {/* Right side actions */}\n          <div className=\"flex items-center space-x-4\">\n            <ThemeToggle />\n            \n            {/* User menu */}\n            <Link\n              href=\"/dashboard\"\n              className=\"hidden md:flex items-center space-x-2 rounded-md bg-primary px-3 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90\"\n            >\n              <User className=\"h-4 w-4\" />\n              <span>Dashboard</span>\n            </Link>\n\n            {/* Mobile menu button */}\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"md:hidden rounded-md p-2 text-muted-foreground hover:bg-accent hover:text-accent-foreground\"\n            >\n              {isMenuOpen ? (\n                <X className=\"h-5 w-5\" />\n              ) : (\n                <Menu className=\"h-5 w-5\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden border-t border-border py-4\">\n            <div className=\"flex flex-col space-y-3\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  onClick={() => setIsMenuOpen(false)}\n                  className={`text-sm font-medium transition-colors hover:text-primary ${\n                    pathname === item.href\n                      ? \"text-primary\"\n                      : \"text-muted-foreground\"\n                  }`}\n                >\n                  {item.label}\n                </Link>\n              ))}\n              <Link\n                href=\"/dashboard\"\n                onClick={() => setIsMenuOpen(false)}\n                className=\"flex items-center space-x-2 rounded-md bg-primary px-3 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90 w-fit\"\n              >\n                <User className=\"h-4 w-4\" />\n                <span>Dashboard</span>\n              </Link>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAQO,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAe,OAAO;QAAa;QAC3C;YAAE,MAAM;YAAgB,OAAO;QAAc;QAC7C;YAAE,MAAM;YAAc,OAAO;QAAc;QAC3C;YAAE,MAAM;YAAe,OAAO;QAAe;KAC9C;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAK,WAAU;8CAAoC;;;;;;;;;;;;sCAMtD,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,yDAAyD,EACnE,aAAa,KAAK,IAAI,GAClB,iBACA,yBACJ;8CAED,KAAK,KAAK;mCARN,KAAK,IAAI;;;;;;;;;;sCAcpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,qIAAA,CAAA,cAAW;;;;;8CAGZ,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;sDAAK;;;;;;;;;;;;8CAIR,8OAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;8CAET,2BACC,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;6DAEb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAOvB,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,cAAc;oCAC7B,WAAW,CAAC,yDAAyD,EACnE,aAAa,KAAK,IAAI,GAClB,iBACA,yBACJ;8CAED,KAAK,KAAK;mCATN,KAAK,IAAI;;;;;0CAYlB,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,SAAS,IAAM,cAAc;gCAC7B,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB", "debugId": null}}]}