import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: string | Date): string {
  const d = new Date(date)
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

export function formatRelativeTime(date: string | Date): string {
  const now = new Date()
  const d = new Date(date)
  const diffInMs = now.getTime() - d.getTime()
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24))
  
  if (diffInDays === 0) {
    return 'Today'
  } else if (diffInDays === 1) {
    return 'Yesterday'
  } else if (diffInDays < 7) {
    return `${diffInDays} days ago`
  } else if (diffInDays < 30) {
    const weeks = Math.floor(diffInDays / 7)
    return `${weeks} week${weeks > 1 ? 's' : ''} ago`
  } else {
    const months = Math.floor(diffInDays / 30)
    return `${months} month${months > 1 ? 's' : ''} ago`
  }
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export function validateUMTEmail(email: string): boolean {
  return email.endsWith('@umt.edu.pk')
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength) + '...'
}

export function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export function isValidImageFile(file: File): boolean {
  const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
  return validTypes.includes(file.type)
}

export function isValidFileSize(file: File, maxSizeInMB: number = 10): boolean {
  const maxSizeInBytes = maxSizeInMB * 1024 * 1024
  return file.size <= maxSizeInBytes
}

export const ITEM_CATEGORIES = [
  'Electronics',
  'Books',
  'Personal Items',
  'Clothing',
  'Accessories',
  'Bags',
  'Documents',
  'Keys',
  'Other'
] as const

export const CAMPUS_LOCATIONS = [
  'Main Library',
  'Cafeteria',
  'Engineering Building',
  'Business Building',
  'Computer Lab 1',
  'Computer Lab 2',
  'Gymnasium',
  'Main Parking Lot',
  'Student Center',
  'Lecture Hall A',
  'Lecture Hall B',
  'Other'
] as const

export type ItemCategory = typeof ITEM_CATEGORIES[number]
export type CampusLocation = typeof CAMPUS_LOCATIONS[number]

export interface LostItem {
  id: string
  title: string
  description: string
  category: ItemCategory
  dateLost: string
  location: CampusLocation
  contactInfo: string
  image?: string
  status: 'active' | 'found' | 'closed'
  userId: string
  createdAt: string
  updatedAt: string
}

export interface FoundItem {
  id: string
  title: string
  description: string
  category: ItemCategory
  dateFound: string
  location: CampusLocation
  contactInfo: string
  image?: string
  status: 'available' | 'claimed' | 'returned'
  userId: string
  createdAt: string
  updatedAt: string
}

export interface User {
  id: string
  firstName: string
  lastName: string
  email: string
  phone?: string
  role: 'student' | 'faculty' | 'staff' | 'admin'
  isVerified: boolean
  createdAt: string
  updatedAt: string
}

export interface ClaimRequest {
  id: string
  itemId: string
  itemType: 'lost' | 'found'
  claimerId: string
  message: string
  status: 'pending' | 'approved' | 'rejected'
  createdAt: string
  updatedAt: string
}
