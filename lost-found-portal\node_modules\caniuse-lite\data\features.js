module.exports={"aac":require("./features/aac"),"abortcontroller":require("./features/abortcontroller"),"ac3-ec3":require("./features/ac3-ec3"),"accelerometer":require("./features/accelerometer"),"addeventlistener":require("./features/addeventlistener"),"alternate-stylesheet":require("./features/alternate-stylesheet"),"ambient-light":require("./features/ambient-light"),"apng":require("./features/apng"),"array-find-index":require("./features/array-find-index"),"array-find":require("./features/array-find"),"array-flat":require("./features/array-flat"),"array-includes":require("./features/array-includes"),"arrow-functions":require("./features/arrow-functions"),"asmjs":require("./features/asmjs"),"async-clipboard":require("./features/async-clipboard"),"async-functions":require("./features/async-functions"),"atob-btoa":require("./features/atob-btoa"),"audio-api":require("./features/audio-api"),"audio":require("./features/audio"),"audiotracks":require("./features/audiotracks"),"autofocus":require("./features/autofocus"),"auxclick":require("./features/auxclick"),"av1":require("./features/av1"),"avif":require("./features/avif"),"background-attachment":require("./features/background-attachment"),"background-clip-text":require("./features/background-clip-text"),"background-img-opts":require("./features/background-img-opts"),"background-position-x-y":require("./features/background-position-x-y"),"background-repeat-round-space":require("./features/background-repeat-round-space"),"background-sync":require("./features/background-sync"),"battery-status":require("./features/battery-status"),"beacon":require("./features/beacon"),"beforeafterprint":require("./features/beforeafterprint"),"bigint":require("./features/bigint"),"blobbuilder":require("./features/blobbuilder"),"bloburls":require("./features/bloburls"),"border-image":require("./features/border-image"),"border-radius":require("./features/border-radius"),"broadcastchannel":require("./features/broadcastchannel"),"brotli":require("./features/brotli"),"calc":require("./features/calc"),"canvas-blending":require("./features/canvas-blending"),"canvas-text":require("./features/canvas-text"),"canvas":require("./features/canvas"),"ch-unit":require("./features/ch-unit"),"chacha20-poly1305":require("./features/chacha20-poly1305"),"channel-messaging":require("./features/channel-messaging"),"childnode-remove":require("./features/childnode-remove"),"classlist":require("./features/classlist"),"client-hints-dpr-width-viewport":require("./features/client-hints-dpr-width-viewport"),"clipboard":require("./features/clipboard"),"colr-v1":require("./features/colr-v1"),"colr":require("./features/colr"),"comparedocumentposition":require("./features/comparedocumentposition"),"console-basic":require("./features/console-basic"),"console-time":require("./features/console-time"),"const":require("./features/const"),"constraint-validation":require("./features/constraint-validation"),"contenteditable":require("./features/contenteditable"),"contentsecuritypolicy":require("./features/contentsecuritypolicy"),"contentsecuritypolicy2":require("./features/contentsecuritypolicy2"),"cookie-store-api":require("./features/cookie-store-api"),"cors":require("./features/cors"),"createimagebitmap":require("./features/createimagebitmap"),"credential-management":require("./features/credential-management"),"cross-document-view-transitions":require("./features/cross-document-view-transitions"),"cryptography":require("./features/cryptography"),"css-all":require("./features/css-all"),"css-anchor-positioning":require("./features/css-anchor-positioning"),"css-animation":require("./features/css-animation"),"css-any-link":require("./features/css-any-link"),"css-appearance":require("./features/css-appearance"),"css-at-counter-style":require("./features/css-at-counter-style"),"css-autofill":require("./features/css-autofill"),"css-backdrop-filter":require("./features/css-backdrop-filter"),"css-background-offsets":require("./features/css-background-offsets"),"css-backgroundblendmode":require("./features/css-backgroundblendmode"),"css-boxdecorationbreak":require("./features/css-boxdecorationbreak"),"css-boxshadow":require("./features/css-boxshadow"),"css-canvas":require("./features/css-canvas"),"css-caret-color":require("./features/css-caret-color"),"css-cascade-layers":require("./features/css-cascade-layers"),"css-cascade-scope":require("./features/css-cascade-scope"),"css-case-insensitive":require("./features/css-case-insensitive"),"css-clip-path":require("./features/css-clip-path"),"css-color-adjust":require("./features/css-color-adjust"),"css-color-function":require("./features/css-color-function"),"css-conic-gradients":require("./features/css-conic-gradients"),"css-container-queries-style":require("./features/css-container-queries-style"),"css-container-queries":require("./features/css-container-queries"),"css-container-query-units":require("./features/css-container-query-units"),"css-containment":require("./features/css-containment"),"css-content-visibility":require("./features/css-content-visibility"),"css-counters":require("./features/css-counters"),"css-crisp-edges":require("./features/css-crisp-edges"),"css-cross-fade":require("./features/css-cross-fade"),"css-default-pseudo":require("./features/css-default-pseudo"),"css-descendant-gtgt":require("./features/css-descendant-gtgt"),"css-deviceadaptation":require("./features/css-deviceadaptation"),"css-dir-pseudo":require("./features/css-dir-pseudo"),"css-display-contents":require("./features/css-display-contents"),"css-element-function":require("./features/css-element-function"),"css-env-function":require("./features/css-env-function"),"css-exclusions":require("./features/css-exclusions"),"css-featurequeries":require("./features/css-featurequeries"),"css-file-selector-button":require("./features/css-file-selector-button"),"css-filter-function":require("./features/css-filter-function"),"css-filters":require("./features/css-filters"),"css-first-letter":require("./features/css-first-letter"),"css-first-line":require("./features/css-first-line"),"css-fixed":require("./features/css-fixed"),"css-focus-visible":require("./features/css-focus-visible"),"css-focus-within":require("./features/css-focus-within"),"css-font-palette":require("./features/css-font-palette"),"css-font-rendering-controls":require("./features/css-font-rendering-controls"),"css-font-stretch":require("./features/css-font-stretch"),"css-gencontent":require("./features/css-gencontent"),"css-gradients":require("./features/css-gradients"),"css-grid-animation":require("./features/css-grid-animation"),"css-grid":require("./features/css-grid"),"css-hanging-punctuation":require("./features/css-hanging-punctuation"),"css-has":require("./features/css-has"),"css-hyphens":require("./features/css-hyphens"),"css-image-orientation":require("./features/css-image-orientation"),"css-image-set":require("./features/css-image-set"),"css-in-out-of-range":require("./features/css-in-out-of-range"),"css-indeterminate-pseudo":require("./features/css-indeterminate-pseudo"),"css-initial-letter":require("./features/css-initial-letter"),"css-initial-value":require("./features/css-initial-value"),"css-lch-lab":require("./features/css-lch-lab"),"css-letter-spacing":require("./features/css-letter-spacing"),"css-line-clamp":require("./features/css-line-clamp"),"css-logical-props":require("./features/css-logical-props"),"css-marker-pseudo":require("./features/css-marker-pseudo"),"css-masks":require("./features/css-masks"),"css-matches-pseudo":require("./features/css-matches-pseudo"),"css-math-functions":require("./features/css-math-functions"),"css-media-interaction":require("./features/css-media-interaction"),"css-media-range-syntax":require("./features/css-media-range-syntax"),"css-media-resolution":require("./features/css-media-resolution"),"css-media-scripting":require("./features/css-media-scripting"),"css-mediaqueries":require("./features/css-mediaqueries"),"css-mixblendmode":require("./features/css-mixblendmode"),"css-module-scripts":require("./features/css-module-scripts"),"css-motion-paths":require("./features/css-motion-paths"),"css-namespaces":require("./features/css-namespaces"),"css-nesting":require("./features/css-nesting"),"css-not-sel-list":require("./features/css-not-sel-list"),"css-nth-child-of":require("./features/css-nth-child-of"),"css-opacity":require("./features/css-opacity"),"css-optional-pseudo":require("./features/css-optional-pseudo"),"css-overflow-anchor":require("./features/css-overflow-anchor"),"css-overflow-overlay":require("./features/css-overflow-overlay"),"css-overflow":require("./features/css-overflow"),"css-overscroll-behavior":require("./features/css-overscroll-behavior"),"css-page-break":require("./features/css-page-break"),"css-paged-media":require("./features/css-paged-media"),"css-paint-api":require("./features/css-paint-api"),"css-placeholder-shown":require("./features/css-placeholder-shown"),"css-placeholder":require("./features/css-placeholder"),"css-print-color-adjust":require("./features/css-print-color-adjust"),"css-read-only-write":require("./features/css-read-only-write"),"css-rebeccapurple":require("./features/css-rebeccapurple"),"css-reflections":require("./features/css-reflections"),"css-regions":require("./features/css-regions"),"css-relative-colors":require("./features/css-relative-colors"),"css-repeating-gradients":require("./features/css-repeating-gradients"),"css-resize":require("./features/css-resize"),"css-revert-value":require("./features/css-revert-value"),"css-rrggbbaa":require("./features/css-rrggbbaa"),"css-scroll-behavior":require("./features/css-scroll-behavior"),"css-scrollbar":require("./features/css-scrollbar"),"css-sel2":require("./features/css-sel2"),"css-sel3":require("./features/css-sel3"),"css-selection":require("./features/css-selection"),"css-shapes":require("./features/css-shapes"),"css-snappoints":require("./features/css-snappoints"),"css-sticky":require("./features/css-sticky"),"css-subgrid":require("./features/css-subgrid"),"css-supports-api":require("./features/css-supports-api"),"css-table":require("./features/css-table"),"css-text-align-last":require("./features/css-text-align-last"),"css-text-box-trim":require("./features/css-text-box-trim"),"css-text-indent":require("./features/css-text-indent"),"css-text-justify":require("./features/css-text-justify"),"css-text-orientation":require("./features/css-text-orientation"),"css-text-spacing":require("./features/css-text-spacing"),"css-text-wrap-balance":require("./features/css-text-wrap-balance"),"css-textshadow":require("./features/css-textshadow"),"css-touch-action":require("./features/css-touch-action"),"css-transitions":require("./features/css-transitions"),"css-unicode-bidi":require("./features/css-unicode-bidi"),"css-unset-value":require("./features/css-unset-value"),"css-variables":require("./features/css-variables"),"css-when-else":require("./features/css-when-else"),"css-widows-orphans":require("./features/css-widows-orphans"),"css-width-stretch":require("./features/css-width-stretch"),"css-writing-mode":require("./features/css-writing-mode"),"css-zoom":require("./features/css-zoom"),"css3-attr":require("./features/css3-attr"),"css3-boxsizing":require("./features/css3-boxsizing"),"css3-colors":require("./features/css3-colors"),"css3-cursors-grab":require("./features/css3-cursors-grab"),"css3-cursors-newer":require("./features/css3-cursors-newer"),"css3-cursors":require("./features/css3-cursors"),"css3-tabsize":require("./features/css3-tabsize"),"currentcolor":require("./features/currentcolor"),"custom-elements":require("./features/custom-elements"),"custom-elementsv1":require("./features/custom-elementsv1"),"customevent":require("./features/customevent"),"datalist":require("./features/datalist"),"dataset":require("./features/dataset"),"datauri":require("./features/datauri"),"date-tolocaledatestring":require("./features/date-tolocaledatestring"),"declarative-shadow-dom":require("./features/declarative-shadow-dom"),"decorators":require("./features/decorators"),"details":require("./features/details"),"deviceorientation":require("./features/deviceorientation"),"devicepixelratio":require("./features/devicepixelratio"),"dialog":require("./features/dialog"),"dispatchevent":require("./features/dispatchevent"),"dnssec":require("./features/dnssec"),"do-not-track":require("./features/do-not-track"),"document-currentscript":require("./features/document-currentscript"),"document-evaluate-xpath":require("./features/document-evaluate-xpath"),"document-execcommand":require("./features/document-execcommand"),"document-policy":require("./features/document-policy"),"document-scrollingelement":require("./features/document-scrollingelement"),"documenthead":require("./features/documenthead"),"dom-manip-convenience":require("./features/dom-manip-convenience"),"dom-range":require("./features/dom-range"),"domcontentloaded":require("./features/domcontentloaded"),"dommatrix":require("./features/dommatrix"),"download":require("./features/download"),"dragndrop":require("./features/dragndrop"),"element-closest":require("./features/element-closest"),"element-from-point":require("./features/element-from-point"),"element-scroll-methods":require("./features/element-scroll-methods"),"eme":require("./features/eme"),"eot":require("./features/eot"),"es5":require("./features/es5"),"es6-class":require("./features/es6-class"),"es6-generators":require("./features/es6-generators"),"es6-module-dynamic-import":require("./features/es6-module-dynamic-import"),"es6-module":require("./features/es6-module"),"es6-number":require("./features/es6-number"),"es6-string-includes":require("./features/es6-string-includes"),"es6":require("./features/es6"),"eventsource":require("./features/eventsource"),"extended-system-fonts":require("./features/extended-system-fonts"),"feature-policy":require("./features/feature-policy"),"fetch":require("./features/fetch"),"fieldset-disabled":require("./features/fieldset-disabled"),"fileapi":require("./features/fileapi"),"filereader":require("./features/filereader"),"filereadersync":require("./features/filereadersync"),"filesystem":require("./features/filesystem"),"flac":require("./features/flac"),"flexbox-gap":require("./features/flexbox-gap"),"flexbox":require("./features/flexbox"),"flow-root":require("./features/flow-root"),"focusin-focusout-events":require("./features/focusin-focusout-events"),"font-family-system-ui":require("./features/font-family-system-ui"),"font-feature":require("./features/font-feature"),"font-kerning":require("./features/font-kerning"),"font-loading":require("./features/font-loading"),"font-size-adjust":require("./features/font-size-adjust"),"font-smooth":require("./features/font-smooth"),"font-unicode-range":require("./features/font-unicode-range"),"font-variant-alternates":require("./features/font-variant-alternates"),"font-variant-numeric":require("./features/font-variant-numeric"),"fontface":require("./features/fontface"),"form-attribute":require("./features/form-attribute"),"form-submit-attributes":require("./features/form-submit-attributes"),"form-validation":require("./features/form-validation"),"forms":require("./features/forms"),"fullscreen":require("./features/fullscreen"),"gamepad":require("./features/gamepad"),"geolocation":require("./features/geolocation"),"getboundingclientrect":require("./features/getboundingclientrect"),"getcomputedstyle":require("./features/getcomputedstyle"),"getelementsbyclassname":require("./features/getelementsbyclassname"),"getrandomvalues":require("./features/getrandomvalues"),"gyroscope":require("./features/gyroscope"),"hardwareconcurrency":require("./features/hardwareconcurrency"),"hashchange":require("./features/hashchange"),"heif":require("./features/heif"),"hevc":require("./features/hevc"),"hidden":require("./features/hidden"),"high-resolution-time":require("./features/high-resolution-time"),"history":require("./features/history"),"html-media-capture":require("./features/html-media-capture"),"html5semantic":require("./features/html5semantic"),"http-live-streaming":require("./features/http-live-streaming"),"http2":require("./features/http2"),"http3":require("./features/http3"),"iframe-sandbox":require("./features/iframe-sandbox"),"iframe-seamless":require("./features/iframe-seamless"),"iframe-srcdoc":require("./features/iframe-srcdoc"),"imagecapture":require("./features/imagecapture"),"ime":require("./features/ime"),"img-naturalwidth-naturalheight":require("./features/img-naturalwidth-naturalheight"),"import-maps":require("./features/import-maps"),"imports":require("./features/imports"),"indeterminate-checkbox":require("./features/indeterminate-checkbox"),"indexeddb":require("./features/indexeddb"),"indexeddb2":require("./features/indexeddb2"),"inline-block":require("./features/inline-block"),"innertext":require("./features/innertext"),"input-autocomplete-onoff":require("./features/input-autocomplete-onoff"),"input-color":require("./features/input-color"),"input-datetime":require("./features/input-datetime"),"input-email-tel-url":require("./features/input-email-tel-url"),"input-event":require("./features/input-event"),"input-file-accept":require("./features/input-file-accept"),"input-file-directory":require("./features/input-file-directory"),"input-file-multiple":require("./features/input-file-multiple"),"input-inputmode":require("./features/input-inputmode"),"input-minlength":require("./features/input-minlength"),"input-number":require("./features/input-number"),"input-pattern":require("./features/input-pattern"),"input-placeholder":require("./features/input-placeholder"),"input-range":require("./features/input-range"),"input-search":require("./features/input-search"),"input-selection":require("./features/input-selection"),"insert-adjacent":require("./features/insert-adjacent"),"insertadjacenthtml":require("./features/insertadjacenthtml"),"internationalization":require("./features/internationalization"),"intersectionobserver-v2":require("./features/intersectionobserver-v2"),"intersectionobserver":require("./features/intersectionobserver"),"intl-pluralrules":require("./features/intl-pluralrules"),"intrinsic-width":require("./features/intrinsic-width"),"jpeg2000":require("./features/jpeg2000"),"jpegxl":require("./features/jpegxl"),"jpegxr":require("./features/jpegxr"),"js-regexp-lookbehind":require("./features/js-regexp-lookbehind"),"json":require("./features/json"),"justify-content-space-evenly":require("./features/justify-content-space-evenly"),"kerning-pairs-ligatures":require("./features/kerning-pairs-ligatures"),"keyboardevent-charcode":require("./features/keyboardevent-charcode"),"keyboardevent-code":require("./features/keyboardevent-code"),"keyboardevent-getmodifierstate":require("./features/keyboardevent-getmodifierstate"),"keyboardevent-key":require("./features/keyboardevent-key"),"keyboardevent-location":require("./features/keyboardevent-location"),"keyboardevent-which":require("./features/keyboardevent-which"),"lazyload":require("./features/lazyload"),"let":require("./features/let"),"link-icon-png":require("./features/link-icon-png"),"link-icon-svg":require("./features/link-icon-svg"),"link-rel-dns-prefetch":require("./features/link-rel-dns-prefetch"),"link-rel-modulepreload":require("./features/link-rel-modulepreload"),"link-rel-preconnect":require("./features/link-rel-preconnect"),"link-rel-prefetch":require("./features/link-rel-prefetch"),"link-rel-preload":require("./features/link-rel-preload"),"link-rel-prerender":require("./features/link-rel-prerender"),"loading-lazy-attr":require("./features/loading-lazy-attr"),"localecompare":require("./features/localecompare"),"magnetometer":require("./features/magnetometer"),"matchesselector":require("./features/matchesselector"),"matchmedia":require("./features/matchmedia"),"mathml":require("./features/mathml"),"maxlength":require("./features/maxlength"),"mdn-css-backdrop-pseudo-element":require("./features/mdn-css-backdrop-pseudo-element"),"mdn-css-unicode-bidi-isolate-override":require("./features/mdn-css-unicode-bidi-isolate-override"),"mdn-css-unicode-bidi-isolate":require("./features/mdn-css-unicode-bidi-isolate"),"mdn-css-unicode-bidi-plaintext":require("./features/mdn-css-unicode-bidi-plaintext"),"mdn-text-decoration-color":require("./features/mdn-text-decoration-color"),"mdn-text-decoration-line":require("./features/mdn-text-decoration-line"),"mdn-text-decoration-shorthand":require("./features/mdn-text-decoration-shorthand"),"mdn-text-decoration-style":require("./features/mdn-text-decoration-style"),"media-fragments":require("./features/media-fragments"),"mediacapture-fromelement":require("./features/mediacapture-fromelement"),"mediarecorder":require("./features/mediarecorder"),"mediasource":require("./features/mediasource"),"menu":require("./features/menu"),"meta-theme-color":require("./features/meta-theme-color"),"meter":require("./features/meter"),"midi":require("./features/midi"),"minmaxwh":require("./features/minmaxwh"),"mp3":require("./features/mp3"),"mpeg-dash":require("./features/mpeg-dash"),"mpeg4":require("./features/mpeg4"),"multibackgrounds":require("./features/multibackgrounds"),"multicolumn":require("./features/multicolumn"),"mutation-events":require("./features/mutation-events"),"mutationobserver":require("./features/mutationobserver"),"namevalue-storage":require("./features/namevalue-storage"),"native-filesystem-api":require("./features/native-filesystem-api"),"nav-timing":require("./features/nav-timing"),"netinfo":require("./features/netinfo"),"notifications":require("./features/notifications"),"object-entries":require("./features/object-entries"),"object-fit":require("./features/object-fit"),"object-observe":require("./features/object-observe"),"object-values":require("./features/object-values"),"objectrtc":require("./features/objectrtc"),"offline-apps":require("./features/offline-apps"),"offscreencanvas":require("./features/offscreencanvas"),"ogg-vorbis":require("./features/ogg-vorbis"),"ogv":require("./features/ogv"),"ol-reversed":require("./features/ol-reversed"),"once-event-listener":require("./features/once-event-listener"),"online-status":require("./features/online-status"),"opus":require("./features/opus"),"orientation-sensor":require("./features/orientation-sensor"),"outline":require("./features/outline"),"pad-start-end":require("./features/pad-start-end"),"page-transition-events":require("./features/page-transition-events"),"pagevisibility":require("./features/pagevisibility"),"passive-event-listener":require("./features/passive-event-listener"),"passkeys":require("./features/passkeys"),"passwordrules":require("./features/passwordrules"),"path2d":require("./features/path2d"),"payment-request":require("./features/payment-request"),"pdf-viewer":require("./features/pdf-viewer"),"permissions-api":require("./features/permissions-api"),"permissions-policy":require("./features/permissions-policy"),"picture-in-picture":require("./features/picture-in-picture"),"picture":require("./features/picture"),"ping":require("./features/ping"),"png-alpha":require("./features/png-alpha"),"pointer-events":require("./features/pointer-events"),"pointer":require("./features/pointer"),"pointerlock":require("./features/pointerlock"),"portals":require("./features/portals"),"prefers-color-scheme":require("./features/prefers-color-scheme"),"prefers-reduced-motion":require("./features/prefers-reduced-motion"),"progress":require("./features/progress"),"promise-finally":require("./features/promise-finally"),"promises":require("./features/promises"),"proximity":require("./features/proximity"),"proxy":require("./features/proxy"),"publickeypinning":require("./features/publickeypinning"),"push-api":require("./features/push-api"),"queryselector":require("./features/queryselector"),"readonly-attr":require("./features/readonly-attr"),"referrer-policy":require("./features/referrer-policy"),"registerprotocolhandler":require("./features/registerprotocolhandler"),"rel-noopener":require("./features/rel-noopener"),"rel-noreferrer":require("./features/rel-noreferrer"),"rellist":require("./features/rellist"),"rem":require("./features/rem"),"requestanimationframe":require("./features/requestanimationframe"),"requestidlecallback":require("./features/requestidlecallback"),"resizeobserver":require("./features/resizeobserver"),"resource-timing":require("./features/resource-timing"),"rest-parameters":require("./features/rest-parameters"),"rtcpeerconnection":require("./features/rtcpeerconnection"),"ruby":require("./features/ruby"),"run-in":require("./features/run-in"),"same-site-cookie-attribute":require("./features/same-site-cookie-attribute"),"screen-orientation":require("./features/screen-orientation"),"script-async":require("./features/script-async"),"script-defer":require("./features/script-defer"),"scrollintoview":require("./features/scrollintoview"),"scrollintoviewifneeded":require("./features/scrollintoviewifneeded"),"sdch":require("./features/sdch"),"selection-api":require("./features/selection-api"),"selectlist":require("./features/selectlist"),"server-timing":require("./features/server-timing"),"serviceworkers":require("./features/serviceworkers"),"setimmediate":require("./features/setimmediate"),"shadowdom":require("./features/shadowdom"),"shadowdomv1":require("./features/shadowdomv1"),"sharedarraybuffer":require("./features/sharedarraybuffer"),"sharedworkers":require("./features/sharedworkers"),"sni":require("./features/sni"),"spdy":require("./features/spdy"),"speech-recognition":require("./features/speech-recognition"),"speech-synthesis":require("./features/speech-synthesis"),"spellcheck-attribute":require("./features/spellcheck-attribute"),"sql-storage":require("./features/sql-storage"),"srcset":require("./features/srcset"),"stream":require("./features/stream"),"streams":require("./features/streams"),"stricttransportsecurity":require("./features/stricttransportsecurity"),"style-scoped":require("./features/style-scoped"),"subresource-bundling":require("./features/subresource-bundling"),"subresource-integrity":require("./features/subresource-integrity"),"svg-css":require("./features/svg-css"),"svg-filters":require("./features/svg-filters"),"svg-fonts":require("./features/svg-fonts"),"svg-fragment":require("./features/svg-fragment"),"svg-html":require("./features/svg-html"),"svg-html5":require("./features/svg-html5"),"svg-img":require("./features/svg-img"),"svg-smil":require("./features/svg-smil"),"svg":require("./features/svg"),"sxg":require("./features/sxg"),"tabindex-attr":require("./features/tabindex-attr"),"template-literals":require("./features/template-literals"),"template":require("./features/template"),"temporal":require("./features/temporal"),"testfeat":require("./features/testfeat"),"text-decoration":require("./features/text-decoration"),"text-emphasis":require("./features/text-emphasis"),"text-overflow":require("./features/text-overflow"),"text-size-adjust":require("./features/text-size-adjust"),"text-stroke":require("./features/text-stroke"),"textcontent":require("./features/textcontent"),"textencoder":require("./features/textencoder"),"tls1-1":require("./features/tls1-1"),"tls1-2":require("./features/tls1-2"),"tls1-3":require("./features/tls1-3"),"touch":require("./features/touch"),"transforms2d":require("./features/transforms2d"),"transforms3d":require("./features/transforms3d"),"trusted-types":require("./features/trusted-types"),"ttf":require("./features/ttf"),"typedarrays":require("./features/typedarrays"),"u2f":require("./features/u2f"),"unhandledrejection":require("./features/unhandledrejection"),"upgradeinsecurerequests":require("./features/upgradeinsecurerequests"),"url-scroll-to-text-fragment":require("./features/url-scroll-to-text-fragment"),"url":require("./features/url"),"urlsearchparams":require("./features/urlsearchparams"),"use-strict":require("./features/use-strict"),"user-select-none":require("./features/user-select-none"),"user-timing":require("./features/user-timing"),"variable-fonts":require("./features/variable-fonts"),"vector-effect":require("./features/vector-effect"),"vibration":require("./features/vibration"),"video":require("./features/video"),"videotracks":require("./features/videotracks"),"view-transitions":require("./features/view-transitions"),"viewport-unit-variants":require("./features/viewport-unit-variants"),"viewport-units":require("./features/viewport-units"),"wai-aria":require("./features/wai-aria"),"wake-lock":require("./features/wake-lock"),"wasm-bigint":require("./features/wasm-bigint"),"wasm-bulk-memory":require("./features/wasm-bulk-memory"),"wasm-extended-const":require("./features/wasm-extended-const"),"wasm-gc":require("./features/wasm-gc"),"wasm-multi-memory":require("./features/wasm-multi-memory"),"wasm-multi-value":require("./features/wasm-multi-value"),"wasm-mutable-globals":require("./features/wasm-mutable-globals"),"wasm-nontrapping-fptoint":require("./features/wasm-nontrapping-fptoint"),"wasm-reference-types":require("./features/wasm-reference-types"),"wasm-relaxed-simd":require("./features/wasm-relaxed-simd"),"wasm-signext":require("./features/wasm-signext"),"wasm-simd":require("./features/wasm-simd"),"wasm-tail-calls":require("./features/wasm-tail-calls"),"wasm-threads":require("./features/wasm-threads"),"wasm":require("./features/wasm"),"wav":require("./features/wav"),"wbr-element":require("./features/wbr-element"),"web-animation":require("./features/web-animation"),"web-app-manifest":require("./features/web-app-manifest"),"web-bluetooth":require("./features/web-bluetooth"),"web-serial":require("./features/web-serial"),"web-share":require("./features/web-share"),"webauthn":require("./features/webauthn"),"webcodecs":require("./features/webcodecs"),"webgl":require("./features/webgl"),"webgl2":require("./features/webgl2"),"webgpu":require("./features/webgpu"),"webhid":require("./features/webhid"),"webkit-user-drag":require("./features/webkit-user-drag"),"webm":require("./features/webm"),"webnfc":require("./features/webnfc"),"webp":require("./features/webp"),"websockets":require("./features/websockets"),"webtransport":require("./features/webtransport"),"webusb":require("./features/webusb"),"webvr":require("./features/webvr"),"webvtt":require("./features/webvtt"),"webworkers":require("./features/webworkers"),"webxr":require("./features/webxr"),"will-change":require("./features/will-change"),"woff":require("./features/woff"),"woff2":require("./features/woff2"),"word-break":require("./features/word-break"),"wordwrap":require("./features/wordwrap"),"x-doc-messaging":require("./features/x-doc-messaging"),"x-frame-options":require("./features/x-frame-options"),"xhr2":require("./features/xhr2"),"xhtml":require("./features/xhtml"),"xhtmlsmil":require("./features/xhtmlsmil"),"xml-serializer":require("./features/xml-serializer"),"zstd":require("./features/zstd")};
