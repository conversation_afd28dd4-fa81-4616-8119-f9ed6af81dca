import type { TSESLint, TSESTree } from '@typescript-eslint/utils';
import type { InferMessageIdsTypeFromRule, InferOptionsTypeFromRule } from '../util';
declare const baseRule: TSESLint.RuleModule<"unsafeRefs", [], unknown, {
    ArrowFunctionExpression(node: TSESTree.ArrowFunctionExpression): void;
    FunctionDeclaration(node: TSESTree.FunctionDeclaration): void;
    FunctionExpression(node: TSESTree.FunctionExpression): void;
}>;
export type Options = InferOptionsTypeFromRule<typeof baseRule>;
export type MessageIds = InferMessageIdsTypeFromRule<typeof baseRule>;
declare const _default: TSESLint.RuleModule<"unsafeRefs", [], import("../../rules").ESLintPluginDocs, TSESLint.RuleListener>;
export default _default;
//# sourceMappingURL=no-loop-func.d.ts.map