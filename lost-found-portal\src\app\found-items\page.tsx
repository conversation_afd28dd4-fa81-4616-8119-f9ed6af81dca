"use client"

import { useState } from "react"
import { Search, Filter, Calendar, MapPin, Tag, Eye } from "lucide-react"
import { Navigation } from "@/components/navigation"

// Mock data for demonstration
const mockFoundItems = [
  {
    id: 1,
    title: "Red Backpack",
    description: "Found a red Jansport backpack near the parking lot. Contains notebooks and a calculator.",
    category: "Bags",
    dateFound: "2024-01-16",
    location: "Main Parking Lot",
    contactInfo: "<EMAIL>",
    image: "/api/placeholder/300/200",
    status: "available"
  },
  {
    id: 2,
    title: "Silver Watch",
    description: "Casio digital watch found in the gymnasium locker room.",
    category: "Accessories",
    dateFound: "2024-01-15",
    location: "Gymnasium",
    contactInfo: "<EMAIL>",
    image: "/api/placeholder/300/200",
    status: "available"
  },
  {
    id: 3,
    title: "USB Flash Drive",
    description: "32GB SanDisk USB drive found in Computer Lab 2.",
    category: "Electronics",
    dateFound: "2024-01-14",
    location: "Computer Lab 2",
    contactInfo: "<EMAIL>",
    image: "/api/placeholder/300/200",
    status: "available"
  },
  {
    id: 4,
    title: "Black Umbrella",
    description: "Large black umbrella left in the lecture hall.",
    category: "Personal Items",
    dateFound: "2024-01-13",
    location: "Lecture Hall A",
    contactInfo: "<EMAIL>",
    image: "/api/placeholder/300/200",
    status: "claimed"
  }
]

const categories = ["All", "Electronics", "Books", "Personal Items", "Clothing", "Accessories", "Bags", "Other"]

export default function FoundItemsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [showClaimed, setShowClaimed] = useState(false)
  const [filteredItems, setFilteredItems] = useState(mockFoundItems.filter(item => item.status === "available"))

  const handleSearch = (term: string) => {
    setSearchTerm(term)
    filterItems(term, selectedCategory, showClaimed)
  }

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category)
    filterItems(searchTerm, category, showClaimed)
  }

  const handleShowClaimedChange = (show: boolean) => {
    setShowClaimed(show)
    filterItems(searchTerm, selectedCategory, show)
  }

  const filterItems = (term: string, category: string, includeClaimed: boolean) => {
    let filtered = mockFoundItems

    if (!includeClaimed) {
      filtered = filtered.filter(item => item.status === "available")
    }

    if (term) {
      filtered = filtered.filter(item =>
        item.title.toLowerCase().includes(term.toLowerCase()) ||
        item.description.toLowerCase().includes(term.toLowerCase()) ||
        item.location.toLowerCase().includes(term.toLowerCase())
      )
    }

    if (category !== "All") {
      filtered = filtered.filter(item => item.category === category)
    }

    setFilteredItems(filtered)
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">Found Items</h1>
          <p className="text-muted-foreground">
            Browse items that have been found and are waiting to be claimed
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8 space-y-4">
          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <input
              type="text"
              placeholder="Search found items..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>

          {/* Category Filter */}
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => handleCategoryChange(category)}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  selectedCategory === category
                    ? "bg-primary text-primary-foreground"
                    : "bg-secondary text-secondary-foreground hover:bg-secondary/80"
                }`}
              >
                {category}
              </button>
            ))}
          </div>

          {/* Show Claimed Toggle */}
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="showClaimed"
              checked={showClaimed}
              onChange={(e) => handleShowClaimedChange(e.target.checked)}
              className="rounded border-border"
            />
            <label htmlFor="showClaimed" className="text-sm text-foreground">
              Show claimed items
            </label>
          </div>
        </div>

        {/* Results Count */}
        <div className="mb-6">
          <p className="text-sm text-muted-foreground">
            Showing {filteredItems.length} found items
          </p>
        </div>

        {/* Items Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredItems.map((item) => (
            <div
              key={item.id}
              className={`bg-card border border-border rounded-lg overflow-hidden hover:shadow-lg transition-shadow ${
                item.status === "claimed" ? "opacity-60" : ""
              }`}
            >
              {/* Item Image */}
              <div className="h-48 bg-muted flex items-center justify-center relative">
                <div className="text-muted-foreground">
                  <Eye className="h-12 w-12" />
                </div>
                {item.status === "claimed" && (
                  <div className="absolute top-2 right-2 bg-destructive text-destructive-foreground px-2 py-1 rounded text-xs font-medium">
                    CLAIMED
                  </div>
                )}
              </div>

              {/* Item Details */}
              <div className="p-4">
                <h3 className="text-lg font-semibold text-card-foreground mb-2">
                  {item.title}
                </h3>
                <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                  {item.description}
                </p>

                <div className="space-y-2 text-sm">
                  <div className="flex items-center text-muted-foreground">
                    <Tag className="h-4 w-4 mr-2" />
                    <span>{item.category}</span>
                  </div>
                  <div className="flex items-center text-muted-foreground">
                    <Calendar className="h-4 w-4 mr-2" />
                    <span>Found on {new Date(item.dateFound).toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center text-muted-foreground">
                    <MapPin className="h-4 w-4 mr-2" />
                    <span>{item.location}</span>
                  </div>
                </div>

                <button 
                  className={`w-full mt-4 py-2 px-4 rounded-md transition-colors ${
                    item.status === "claimed"
                      ? "bg-muted text-muted-foreground cursor-not-allowed"
                      : "bg-primary text-primary-foreground hover:bg-primary/90"
                  }`}
                  disabled={item.status === "claimed"}
                >
                  {item.status === "claimed" ? "Already Claimed" : "Claim This Item"}
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredItems.length === 0 && (
          <div className="text-center py-12">
            <Eye className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-foreground mb-2">
              No items found
            </h3>
            <p className="text-muted-foreground">
              Try adjusting your search terms or filters
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
