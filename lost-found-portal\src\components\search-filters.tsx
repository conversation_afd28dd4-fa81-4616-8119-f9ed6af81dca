"use client"

import { useState } from "react"
import { Search, Filter, X, Calendar, MapPin } from "lucide-react"
import { ITEM_CATEGORIES, CAMPUS_LOCATIONS } from "@/lib/utils"
import type { ItemCategory, CampusLocation } from "@/lib/utils"

interface SearchFiltersProps {
  searchTerm: string
  onSearchChange: (term: string) => void
  selectedCategory: string
  onCategoryChange: (category: string) => void
  selectedLocation?: string
  onLocationChange?: (location: string) => void
  dateRange?: { start: string; end: string }
  onDateRangeChange?: (range: { start: string; end: string }) => void
  showAdvancedFilters?: boolean
}

export function SearchFilters({
  searchTerm,
  onSearchChange,
  selectedCategory,
  onCategoryChange,
  selectedLocation = "",
  onLocationChange,
  dateRange,
  onDateRangeChange,
  showAdvancedFilters = false
}: SearchFiltersProps) {
  const [showFilters, setShowFilters] = useState(false)
  const [localDateRange, setLocalDateRange] = useState(dateRange || { start: "", end: "" })

  const handleDateRangeChange = (field: 'start' | 'end', value: string) => {
    const newRange = { ...localDateRange, [field]: value }
    setLocalDateRange(newRange)
    onDateRangeChange?.(newRange)
  }

  const clearFilters = () => {
    onSearchChange("")
    onCategoryChange("All")
    onLocationChange?.("")
    const emptyRange = { start: "", end: "" }
    setLocalDateRange(emptyRange)
    onDateRangeChange?.(emptyRange)
  }

  const hasActiveFilters = searchTerm || selectedCategory !== "All" || selectedLocation || localDateRange.start || localDateRange.end

  return (
    <div className="space-y-4">
      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
        <input
          type="text"
          placeholder="Search items..."
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          className="w-full pl-10 pr-4 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
        />
      </div>

      {/* Filter Toggle and Clear */}
      <div className="flex items-center justify-between">
        <button
          onClick={() => setShowFilters(!showFilters)}
          className="flex items-center space-x-2 px-4 py-2 border border-border rounded-md bg-background text-foreground hover:bg-accent transition-colors"
        >
          <Filter className="h-4 w-4" />
          <span>Filters</span>
          {hasActiveFilters && (
            <span className="bg-primary text-primary-foreground text-xs px-2 py-1 rounded-full">
              Active
            </span>
          )}
        </button>

        {hasActiveFilters && (
          <button
            onClick={clearFilters}
            className="flex items-center space-x-2 px-4 py-2 text-muted-foreground hover:text-foreground transition-colors"
          >
            <X className="h-4 w-4" />
            <span>Clear all</span>
          </button>
        )}
      </div>

      {/* Category Filter (Always Visible) */}
      <div className="flex flex-wrap gap-2">
        {["All", ...ITEM_CATEGORIES].map((category) => (
          <button
            key={category}
            onClick={() => onCategoryChange(category)}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              selectedCategory === category
                ? "bg-primary text-primary-foreground"
                : "bg-secondary text-secondary-foreground hover:bg-secondary/80"
            }`}
          >
            {category}
          </button>
        ))}
      </div>

      {/* Advanced Filters */}
      {showFilters && showAdvancedFilters && (
        <div className="p-4 border border-border rounded-md bg-card space-y-4">
          <h3 className="font-medium text-card-foreground">Advanced Filters</h3>
          
          {/* Location Filter */}
          {onLocationChange && (
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                <MapPin className="inline h-4 w-4 mr-1" />
                Location
              </label>
              <select
                value={selectedLocation}
                onChange={(e) => onLocationChange(e.target.value)}
                className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="">All locations</option>
                {CAMPUS_LOCATIONS.map((location) => (
                  <option key={location} value={location}>
                    {location}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* Date Range Filter */}
          {onDateRangeChange && (
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                <Calendar className="inline h-4 w-4 mr-1" />
                Date Range
              </label>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="block text-xs text-muted-foreground mb-1">From</label>
                  <input
                    type="date"
                    value={localDateRange.start}
                    onChange={(e) => handleDateRangeChange('start', e.target.value)}
                    className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-xs text-muted-foreground mb-1">To</label>
                  <input
                    type="date"
                    value={localDateRange.end}
                    onChange={(e) => handleDateRangeChange('end', e.target.value)}
                    className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
