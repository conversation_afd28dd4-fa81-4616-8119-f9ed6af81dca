# Lost & Found Portal - UMT Campus

A comprehensive web-based Lost & Found Portal designed for the University of Management and Technology (UMT) campus community. This platform enables students, faculty, and staff to report lost items, browse found items, and connect securely to claim belongings.

## 🌟 Features

### Core Features
- **User Authentication**: Secure login/signup using university email addresses
- **Report Lost Items**: Detailed forms with item descriptions, categories, locations, and image uploads
- **Report Found Items**: Quick posting of found items with photos and location details
- **Advanced Search & Filtering**: Search by keywords, categories, locations, and date ranges
- **Item Listings**: Separate views for lost and found items with comprehensive filtering
- **Claim System**: Secure claiming process with verification
- **User Dashboard**: Personal dashboard to manage posts and claim requests
- **Real-time Notifications**: Updates on item matches and claim requests
- **Responsive Design**: Optimized for both mobile and desktop devices

### Design Features
- **Dark/Light Theme Toggle**: User preference-based theme switching with system detection
- **Modern UI**: Clean, intuitive interface built with Tailwind CSS
- **Accessibility**: WCAG compliant design with proper contrast and navigation
- **Performance**: Optimized loading and smooth interactions

## 🛠️ Technology Stack

- **Frontend**: Next.js 15 with React 18
- **Styling**: Tailwind CSS with custom design system
- **Icons**: Lucide React
- **Theme Management**: next-themes
- **Type Safety**: TypeScript
- **Development**: Turbopack for fast development builds

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd lost-found-portal
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Build for Production

```bash
npm run build
npm start
```

## 📁 Project Structure

```
lost-found-portal/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── auth/              # Authentication pages
│   │   ├── dashboard/         # User dashboard
│   │   ├── found-items/       # Found items listing
│   │   ├── lost-items/        # Lost items listing
│   │   ├── post-found/        # Report found item form
│   │   ├── post-lost/         # Report lost item form
│   │   ├── layout.tsx         # Root layout with theme provider
│   │   ├── page.tsx           # Home page
│   │   └── globals.css        # Global styles
│   ├── components/            # Reusable React components
│   │   ├── item-card.tsx      # Item display card
│   │   ├── navigation.tsx     # Main navigation
│   │   ├── search-filters.tsx # Search and filter component
│   │   ├── theme-provider.tsx # Theme context provider
│   │   └── theme-toggle.tsx   # Theme toggle button
│   └── lib/
│       └── utils.ts           # Utility functions and types
├── public/                    # Static assets
├── tailwind.config.ts         # Tailwind CSS configuration
├── next.config.js            # Next.js configuration
└── package.json              # Dependencies and scripts
```

## 🎨 Design System

The application uses a comprehensive design system with:

- **Color Palette**: Semantic color tokens for consistent theming
- **Typography**: Responsive text scales and font weights
- **Spacing**: Consistent spacing scale using Tailwind utilities
- **Components**: Reusable UI components with consistent styling
- **Dark Mode**: Complete dark theme support with automatic system detection

## 🚧 Future Enhancements

### Planned Features
- **Database Integration**: Supabase for data persistence
- **Authentication System**: NextAuth.js with email verification
- **Real-time Chat**: Secure communication between users
- **Image Similarity AI**: Visual matching for lost/found items
- **Push Notifications**: Browser and email notifications
- **Admin Panel**: Content moderation and user management

### Technical Improvements
- **API Routes**: RESTful API endpoints
- **Database Schema**: Optimized data models
- **File Storage**: Image upload and management
- **Search Engine**: Advanced search capabilities
- **Testing**: Unit and integration tests

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 🏫 About UMT

This portal is designed specifically for the University of Management and Technology (UMT) campus community, ensuring a secure and trusted environment for students, faculty, and staff to recover lost belongings.

---

**Built with ❤️ for the UMT Campus Community**
