export declare const ESLINT_BULK_PATCH_PATH_ENV_VAR_NAME: 'RUSHSTACK_ESLINT_BULK_PATCH_PATH';
export declare const ESLINT_BULK_SUPPRESS_ENV_VAR_NAME: 'RUSHSTACK_ESLINT_BULK_SUPPRESS';
export declare const ESLINT_BULK_ENABLE_ENV_VAR_NAME: 'ESLINT_BULK_ENABLE';
export declare const ESLINT_BULK_PRUNE_ENV_VAR_NAME: 'ESLINT_BULK_PRUNE';
export declare const ESLINT_BULK_DETECT_ENV_VAR_NAME: '_RUSHSTACK_ESLINT_BULK_DETECT';
export declare const ESLINT_BULK_FORCE_REGENERATE_PATCH_ENV_VAR_NAME: 'RUSHSTACK_ESLINT_BULK_FORCE_REGENERATE_PATCH';
export declare const VSCODE_PID_ENV_VAR_NAME: 'VSCODE_PID';
export declare const ESLINT_PACKAGE_NAME_ENV_VAR_NAME: '_RUSHSTACK_ESLINT_PACKAGE_NAME';
export declare const BULK_SUPPRESSIONS_CLI_ESLINT_PACKAGE_NAME: string;
//# sourceMappingURL=constants.d.ts.map