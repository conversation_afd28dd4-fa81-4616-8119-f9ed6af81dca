import { Calendar, MapPin, Tag, Eye, Search, User } from "lucide-react"
import { formatRelativeTime, truncateText } from "@/lib/utils"
import type { LostItem, FoundItem } from "@/lib/utils"

interface ItemCardProps {
  item: LostItem | FoundItem
  type: 'lost' | 'found'
  onContact?: () => void
  onClaim?: () => void
  showActions?: boolean
}

export function ItemCard({ item, type, onContact, onClaim, showActions = true }: ItemCardProps) {
  const isLostItem = type === 'lost'
  const isAvailable = item.status === 'active' || item.status === 'available'
  
  return (
    <div className={`bg-card border border-border rounded-lg overflow-hidden hover:shadow-lg transition-shadow ${
      !isAvailable ? 'opacity-60' : ''
    }`}>
      {/* Item Image */}
      <div className="h-48 bg-muted flex items-center justify-center relative">
        <div className="text-muted-foreground">
          {isLostItem ? (
            <Search className="h-12 w-12" />
          ) : (
            <Eye className="h-12 w-12" />
          )}
        </div>
        
        {/* Status Badge */}
        <div className="absolute top-2 left-2">
          <span className={`px-2 py-1 rounded text-xs font-medium ${
            isLostItem
              ? "bg-destructive/10 text-destructive"
              : "bg-primary/10 text-primary"
          }`}>
            {isLostItem ? 'LOST' : 'FOUND'}
          </span>
        </div>
        
        {/* Status Badge for non-available items */}
        {!isAvailable && (
          <div className="absolute top-2 right-2">
            <span className="bg-muted text-muted-foreground px-2 py-1 rounded text-xs font-medium">
              {item.status.toUpperCase()}
            </span>
          </div>
        )}
      </div>

      {/* Item Details */}
      <div className="p-4">
        <h3 className="text-lg font-semibold text-card-foreground mb-2">
          {item.title}
        </h3>
        <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
          {truncateText(item.description, 100)}
        </p>

        <div className="space-y-2 text-sm">
          <div className="flex items-center text-muted-foreground">
            <Tag className="h-4 w-4 mr-2 flex-shrink-0" />
            <span>{item.category}</span>
          </div>
          <div className="flex items-center text-muted-foreground">
            <Calendar className="h-4 w-4 mr-2 flex-shrink-0" />
            <span>
              {isLostItem ? 'Lost' : 'Found'} {formatRelativeTime(isLostItem ? (item as LostItem).dateLost : (item as FoundItem).dateFound)}
            </span>
          </div>
          <div className="flex items-center text-muted-foreground">
            <MapPin className="h-4 w-4 mr-2 flex-shrink-0" />
            <span>{item.location}</span>
          </div>
        </div>

        {showActions && (
          <div className="mt-4">
            {isLostItem ? (
              <button 
                onClick={onContact}
                className={`w-full py-2 px-4 rounded-md transition-colors ${
                  isAvailable
                    ? "bg-primary text-primary-foreground hover:bg-primary/90"
                    : "bg-muted text-muted-foreground cursor-not-allowed"
                }`}
                disabled={!isAvailable}
              >
                {isAvailable ? "Contact Owner" : "No Longer Available"}
              </button>
            ) : (
              <button 
                onClick={onClaim}
                className={`w-full py-2 px-4 rounded-md transition-colors ${
                  isAvailable
                    ? "bg-primary text-primary-foreground hover:bg-primary/90"
                    : "bg-muted text-muted-foreground cursor-not-allowed"
                }`}
                disabled={!isAvailable}
              >
                {isAvailable ? "Claim This Item" : "Already Claimed"}
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

// Skeleton loader for item cards
export function ItemCardSkeleton() {
  return (
    <div className="bg-card border border-border rounded-lg overflow-hidden">
      <div className="h-48 bg-muted animate-pulse" />
      <div className="p-4 space-y-3">
        <div className="h-6 bg-muted rounded animate-pulse" />
        <div className="space-y-2">
          <div className="h-4 bg-muted rounded animate-pulse" />
          <div className="h-4 bg-muted rounded animate-pulse w-3/4" />
        </div>
        <div className="space-y-2">
          <div className="h-4 bg-muted rounded animate-pulse w-1/2" />
          <div className="h-4 bg-muted rounded animate-pulse w-2/3" />
          <div className="h-4 bg-muted rounded animate-pulse w-1/2" />
        </div>
        <div className="h-10 bg-muted rounded animate-pulse" />
      </div>
    </div>
  )
}
